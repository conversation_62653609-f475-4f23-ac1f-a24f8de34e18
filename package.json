{"name": "legit's auth system", "version": "2.0.0", "description": "Advanced authentication system with anti-cheat, anti-reversing, and enhanced security features", "main": "backend.js", "scripts": {"start": "node backend.js", "dev": "NODE_ENV=development nodemon backend.js", "build": "webpack --mode=production", "build:dev": "webpack --mode=development", "serve": "webpack-dev-server --mode=development", "test": "NODE_ENV=test jest", "init-db": "node scripts/init-database.js", "generate-keys": "node scripts/generate-keys.js"}, "keywords": ["authentication", "anti-cheat", "anti-reversing", "security", "2fa", "hwid", "license-management"], "author": "Security Team", "license": "PROPRIETARY", "dependencies": {"archiver": "^5.3.2", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-mysql-session": "^3.0.3", "express-rate-limit": "^6.11.2", "express-session": "^1.18.1", "geoip-lite": "^1.4.10", "helmet": "^7.2.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "sharp": "^0.32.6", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "speakeasy": "^2.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/preset-env": "^7.21.0", "@babel/preset-react": "^7.18.6", "babel-loader": "^9.1.2", "css-loader": "^6.7.3", "html-webpack-plugin": "^5.5.0", "jest": "^29.7.0", "nodemon": "^2.0.22", "style-loader": "^3.3.2", "supertest": "^6.3.4", "webpack": "^5.76.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/Tellurr/LegitStack.git"}, "bugs": {"url": "https://github.com/Tellurr/LegitStack/issues"}, "homepage": "https://github.com/Tellurr/LegitStack#readme"}